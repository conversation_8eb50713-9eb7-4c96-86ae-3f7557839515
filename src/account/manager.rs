use crate::account::account::{Account, AccountSummary};
use crate::account::types::{AccountConfig, TradeRecord};
use crate::types::{OrderSide, Price, Trade};
use crate::{BacktestError, Result};
use std::collections::HashMap;

/// 账户管理器
pub struct AccountManager {
    /// 账户实例
    account: Account,
    /// 当前市场价格
    current_prices: HashMap<String, Price>,
}

impl AccountManager {
    /// 创建新的账户管理器
    pub fn new(account_id: String, config: AccountConfig) -> Self {
        let account = Account::new(account_id, config);

        Self {
            account,
            current_prices: HashMap::new(),
        }
    }

    /// 处理交易
    pub fn process_trade(&mut self, trade: Trade) -> Result<()> {
        let trade_record = TradeRecord::new(
            trade.id.clone(),
            format!("order_{}", trade.id), // 假设订单ID
            "BTCUSDT".to_string(),         // 假设交易对，实际应该从trade中获取
            trade.side,
            trade.price,
            trade.quantity,
            trade.quantity * trade.price.value() * 0.0004, // 假设手续费率
            "USDT".to_string(),
            false, // 假设为taker
        );

        self.account
            .process_trade(trade_record, &self.current_prices)
            .map_err(|e| BacktestError::Account(e))?;

        Ok(())
    }

    /// 更新市场价格
    pub fn update_price(&mut self, symbol: String, price: Price) {
        self.current_prices.insert(symbol, price);
    }

    /// 获取账户摘要
    pub fn get_account_summary(&self) -> AccountSummary {
        self.account.get_summary(&self.current_prices)
    }

    /// 获取账户余额
    pub fn get_balance(&self, asset: &str) -> f64 {
        self.account.balance_manager.get_available_balance(asset)
    }

    /// 获取仓位信息
    pub fn get_position(&self, symbol: &str) -> Option<&crate::account::position::Position> {
        self.account.get_position(symbol)
    }

    /// 获取账户净值
    pub fn get_net_value(&self) -> f64 {
        self.account.calculate_net_value(&self.current_prices)
    }

    /// 验证账户状态
    pub fn validate_account(&self) -> Result<()> {
        self.account.validate().map_err(|e| BacktestError::Account(e))
    }
}

    /// 启动交易处理任务
    async fn start_trade_processing_task(&mut self) -> Result<()> {
        if let Some(mut trade_rx) = self.trade_rx.take() {
            let account = Arc::clone(&self.account);
            let current_prices = Arc::clone(&self.current_prices);
            let account_update_tx = self.account_update_tx.clone();

            tokio::spawn(async move {
                while let Ok(trade) = trade_rx.recv().await {
                    if let Err(e) = Self::process_trade_internal(
                        &account,
                        &current_prices,
                        &account_update_tx,
                        trade,
                    )
                    .await
                    {
                        error!("Failed to process trade: {}", e);
                    }
                }
            });
        }

        Ok(())
    }

    /// 启动订单处理任务
    async fn start_order_processing_task(&mut self) -> Result<()> {
        if let Some(mut order_rx) = self.order_rx.take() {
            let account = Arc::clone(&self.account);
            let current_prices = Arc::clone(&self.current_prices);

            tokio::spawn(async move {
                while let Some(order) = order_rx.recv().await {
                    if let Err(e) =
                        Self::process_order_internal(&account, &current_prices, order).await
                    {
                        error!("Failed to process order: {}", e);
                    }
                }
            });
        }

        Ok(())
    }

    /// 内部交易处理方法
    async fn process_trade_internal(
        account: &Arc<RwLock<Account>>,
        current_prices: &Arc<RwLock<HashMap<String, Price>>>,
        account_update_tx: &broadcast::Sender<AccountSummary>,
        trade: Trade,
    ) -> Result<()> {
        let trade_record = TradeRecord::new(
            trade.id.clone(),
            format!("order_{}", trade.id), // 假设订单ID
            "BTCUSDT".to_string(),         // 假设交易对，实际应该从trade中获取
            trade.side,
            trade.price,
            trade.quantity,
            trade.quantity * trade.price.value() * 0.0004, // 假设手续费率
            "USDT".to_string(),
            false, // 假设为taker
        );

        let prices = current_prices
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read prices: {}", e)))?
            .clone();

        let mut account_guard = account
            .write()
            .map_err(|e| BacktestError::Account(format!("Failed to write account: {}", e)))?;

        account_guard
            .process_trade(trade_record, &prices)
            .map_err(|e| BacktestError::Account(e))?;

        info!(
            "Processed trade: {} {} {} @ {}",
            trade.id,
            match trade.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            trade.quantity,
            trade.price
        );

        // 发送账户更新
        let summary = account_guard.get_summary(&prices);
        if let Err(e) = account_update_tx.send(summary) {
            debug!("Failed to send account update: {}", e);
        }

        Ok(())
    }

    /// 内部订单处理方法
    async fn process_order_internal(
        account: &Arc<RwLock<Account>>,
        current_prices: &Arc<RwLock<HashMap<String, Price>>>,
        order: Order,
    ) -> Result<()> {
        // 这里可以实现订单验证逻辑
        // 例如检查余额是否足够、风险控制等

        let prices = current_prices
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read prices: {}", e)))?;

        let account_guard = account
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read account: {}", e)))?;

        // 检查是否有足够资金
        let symbol = "BTCUSDT"; // 假设交易对，实际应该从order中获取
        let price = order
            .price
            .unwrap_or_else(|| prices.get(symbol).copied().unwrap_or(Price::new(50000.0)));

        if !account_guard.can_afford_trade(symbol, order.side, price, order.quantity) {
            warn!(
                "Insufficient funds for order {}: {} {} @ {}",
                order.id,
                match order.side {
                    OrderSide::Buy => "BUY",
                    OrderSide::Sell => "SELL",
                },
                order.quantity,
                price
            );
            return Ok(());
        }

        info!(
            "Order validation passed: {} {} {} @ {}",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            price
        );

        Ok(())
    }

    /// 更新市场价格
    pub async fn update_price(&self, symbol: String, price: Price) -> Result<()> {
        let mut prices = self
            .current_prices
            .write()
            .map_err(|e| BacktestError::Account(format!("Failed to write prices: {}", e)))?;

        prices.insert(symbol, price);
        Ok(())
    }

    /// 获取账户摘要
    pub async fn get_account_summary(&self) -> Result<AccountSummary> {
        let prices = self
            .current_prices
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read prices: {}", e)))?;

        let account = self
            .account
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read account: {}", e)))?;

        Ok(account.get_summary(&prices))
    }

    /// 获取账户余额
    pub async fn get_balance(&self, asset: &str) -> Result<f64> {
        let account = self
            .account
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read account: {}", e)))?;

        Ok(account.balance_manager.get_available_balance(asset))
    }

    /// 获取仓位信息
    pub async fn get_position(
        &self,
        symbol: &str,
    ) -> Result<Option<crate::account::position::Position>> {
        let account = self
            .account
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read account: {}", e)))?;

        Ok(account.get_position(symbol).cloned())
    }

    /// 获取账户净值
    pub async fn get_net_value(&self) -> Result<f64> {
        let prices = self
            .current_prices
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read prices: {}", e)))?;

        let account = self
            .account
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read account: {}", e)))?;

        Ok(account.calculate_net_value(&prices))
    }

    /// 验证账户状态
    pub async fn validate_account(&self) -> Result<()> {
        let account = self
            .account
            .read()
            .map_err(|e| BacktestError::Account(format!("Failed to read account: {}", e)))?;

        account.validate().map_err(|e| BacktestError::Account(e))
    }
}
