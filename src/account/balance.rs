use crate::types::Price;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 余额信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Balance {
    /// 资产名称（如 USDT, BTC）
    pub asset: String,
    /// 总余额
    pub total: f64,
    /// 可用余额
    pub available: f64,
    /// 冻结余额（挂单占用）
    pub frozen: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl Balance {
    /// 创建新的余额
    pub fn new(asset: String, total: f64) -> Self {
        Self {
            asset,
            total,
            available: total,
            frozen: 0.0,
            last_updated: Utc::now(),
        }
    }

    /// 创建空余额
    pub fn zero(asset: String) -> Self {
        Self::new(asset, 0.0)
    }

    /// 检查余额是否足够
    pub fn has_sufficient_balance(&self, amount: f64) -> bool {
        self.available >= amount
    }

    /// 冻结余额
    pub fn freeze(&mut self, amount: f64) -> Result<(), String> {
        if amount < 0.0 {
            return Err("Cannot freeze negative amount".to_string());
        }
        
        if self.available < amount {
            return Err(format!(
                "Insufficient available balance: {} < {}",
                self.available, amount
            ));
        }

        self.available -= amount;
        self.frozen += amount;
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 解冻余额
    pub fn unfreeze(&mut self, amount: f64) -> Result<(), String> {
        if amount < 0.0 {
            return Err("Cannot unfreeze negative amount".to_string());
        }
        
        if self.frozen < amount {
            return Err(format!(
                "Insufficient frozen balance: {} < {}",
                self.frozen, amount
            ));
        }

        self.frozen -= amount;
        self.available += amount;
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 增加余额
    pub fn add(&mut self, amount: f64) {
        self.total += amount;
        self.available += amount;
        self.last_updated = Utc::now();
    }

    /// 减少余额（从可用余额中扣除）
    pub fn subtract(&mut self, amount: f64) -> Result<(), String> {
        if amount < 0.0 {
            return Err("Cannot subtract negative amount".to_string());
        }
        
        if self.available < amount {
            return Err(format!(
                "Insufficient available balance: {} < {}",
                self.available, amount
            ));
        }

        self.total -= amount;
        self.available -= amount;
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 从冻结余额中扣除（用于订单成交）
    pub fn subtract_frozen(&mut self, amount: f64) -> Result<(), String> {
        if amount < 0.0 {
            return Err("Cannot subtract negative amount".to_string());
        }
        
        if self.frozen < amount {
            return Err(format!(
                "Insufficient frozen balance: {} < {}",
                self.frozen, amount
            ));
        }

        self.total -= amount;
        self.frozen -= amount;
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 验证余额一致性
    pub fn validate(&self) -> Result<(), String> {
        if self.total < 0.0 {
            return Err("Total balance cannot be negative".to_string());
        }
        
        if self.available < 0.0 {
            return Err("Available balance cannot be negative".to_string());
        }
        
        if self.frozen < 0.0 {
            return Err("Frozen balance cannot be negative".to_string());
        }
        
        let expected_total = self.available + self.frozen;
        if (self.total - expected_total).abs() > 1e-8 {
            return Err(format!(
                "Balance inconsistency: total={}, available+frozen={}",
                self.total, expected_total
            ));
        }
        
        Ok(())
    }
}

/// 多资产余额管理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BalanceManager {
    /// 各资产余额
    pub balances: HashMap<String, Balance>,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl BalanceManager {
    /// 创建新的余额管理器
    pub fn new() -> Self {
        Self {
            balances: HashMap::new(),
            last_updated: Utc::now(),
        }
    }

    /// 初始化资产余额
    pub fn initialize_balance(&mut self, asset: String, amount: f64) {
        self.balances.insert(asset.clone(), Balance::new(asset, amount));
        self.last_updated = Utc::now();
    }

    /// 获取资产余额
    pub fn get_balance(&self, asset: &str) -> Balance {
        self.balances
            .get(asset)
            .cloned()
            .unwrap_or_else(|| Balance::zero(asset.to_string()))
    }

    /// 获取可用余额
    pub fn get_available_balance(&self, asset: &str) -> f64 {
        self.get_balance(asset).available
    }

    /// 获取总余额
    pub fn get_total_balance(&self, asset: &str) -> f64 {
        self.get_balance(asset).total
    }

    /// 检查余额是否足够
    pub fn has_sufficient_balance(&self, asset: &str, amount: f64) -> bool {
        self.get_available_balance(asset) >= amount
    }

    /// 冻结余额
    pub fn freeze_balance(&mut self, asset: &str, amount: f64) -> Result<(), String> {
        let mut balance = self.get_balance(asset);
        balance.freeze(amount)?;
        self.balances.insert(asset.to_string(), balance);
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 解冻余额
    pub fn unfreeze_balance(&mut self, asset: &str, amount: f64) -> Result<(), String> {
        let mut balance = self.get_balance(asset);
        balance.unfreeze(amount)?;
        self.balances.insert(asset.to_string(), balance);
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 增加余额
    pub fn add_balance(&mut self, asset: &str, amount: f64) {
        let mut balance = self.get_balance(asset);
        balance.add(amount);
        self.balances.insert(asset.to_string(), balance);
        self.last_updated = Utc::now();
    }

    /// 减少余额
    pub fn subtract_balance(&mut self, asset: &str, amount: f64) -> Result<(), String> {
        let mut balance = self.get_balance(asset);
        balance.subtract(amount)?;
        self.balances.insert(asset.to_string(), balance);
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 从冻结余额中扣除
    pub fn subtract_frozen_balance(&mut self, asset: &str, amount: f64) -> Result<(), String> {
        let mut balance = self.get_balance(asset);
        balance.subtract_frozen(amount)?;
        self.balances.insert(asset.to_string(), balance);
        self.last_updated = Utc::now();
        Ok(())
    }

    /// 计算总资产价值（以指定资产计价）
    pub fn calculate_total_value(&self, prices: &HashMap<String, Price>, base_asset: &str) -> f64 {
        let mut total_value = 0.0;
        
        for (asset, balance) in &self.balances {
            if asset == base_asset {
                total_value += balance.total;
            } else if let Some(price) = prices.get(asset) {
                total_value += balance.total * price.value();
            }
        }
        
        total_value
    }

    /// 验证所有余额
    pub fn validate_all(&self) -> Result<(), String> {
        for (asset, balance) in &self.balances {
            balance.validate().map_err(|e| format!("Asset {}: {}", asset, e))?;
        }
        Ok(())
    }

    /// 获取所有非零余额
    pub fn get_non_zero_balances(&self) -> HashMap<String, Balance> {
        self.balances
            .iter()
            .filter(|(_, balance)| balance.total > 1e-8)
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect()
    }
}

impl Default for BalanceManager {
    fn default() -> Self {
        Self::new()
    }
}
